{"version": 3, "file": "brace-expressions.js", "sourceRoot": "", "sources": ["../../src/brace-expressions.ts"], "names": [], "mappings": "AAAA,wEAAwE;AACxE,wCAAwC;AAExC,8DAA8D;AAC9D,MAAM,YAAY,GAA0D;IAC1E,WAAW,EAAE,CAAC,sBAAsB,EAAE,IAAI,CAAC;IAC3C,WAAW,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC;IACpC,WAAW,EAAE,CAAC,KAAK,GAAG,QAAQ,GAAG,IAAI,EAAE,KAAK,CAAC;IAC7C,WAAW,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC;IACjC,WAAW,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC;IAC9B,WAAW,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC;IAC9B,WAAW,EAAE,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,WAAW,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC;IAC9B,WAAW,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC;IAC7B,WAAW,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC;IAC7B,WAAW,EAAE,CAAC,uBAAuB,EAAE,IAAI,CAAC;IAC5C,WAAW,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC;IAC9B,UAAU,EAAE,CAAC,6BAA6B,EAAE,IAAI,CAAC;IACjD,YAAY,EAAE,CAAC,WAAW,EAAE,KAAK,CAAC;CACnC,CAAA;AAED,+DAA+D;AAC/D,mBAAmB;AACnB,MAAM,WAAW,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;AACjE,qCAAqC;AACrC,MAAM,YAAY,GAAG,CAAC,CAAS,EAAE,EAAE,CACjC,CAAC,CAAC,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC,CAAA;AAE/C,4DAA4D;AAC5D,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAU,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;AASpE,+DAA+D;AAC/D,kEAAkE;AAClE,mEAAmE;AACnE,yCAAyC;AACzC,iEAAiE;AACjE,6BAA6B;AAC7B,MAAM,CAAC,MAAM,UAAU,GAAG,CACxB,IAAY,EACZ,QAAgB,EACE,EAAE;IACpB,MAAM,GAAG,GAAG,QAAQ,CAAA;IACpB,qBAAqB;IACrB,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE;QAC5B,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IACD,oBAAoB;IACpB,MAAM,MAAM,GAAa,EAAE,CAAA;IAC3B,MAAM,IAAI,GAAa,EAAE,CAAA;IAEzB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA;IACf,IAAI,QAAQ,GAAG,KAAK,CAAA;IACpB,IAAI,KAAK,GAAG,KAAK,CAAA;IACjB,IAAI,QAAQ,GAAG,KAAK,CAAA;IACpB,IAAI,MAAM,GAAG,KAAK,CAAA;IAClB,IAAI,MAAM,GAAG,GAAG,CAAA;IAChB,IAAI,UAAU,GAAG,EAAE,CAAA;IACnB,KAAK,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE;QAC7B,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QACxB,IAAI,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,EAAE;YAC7C,MAAM,GAAG,IAAI,CAAA;YACb,CAAC,EAAE,CAAA;YACH,SAAQ;SACT;QAED,IAAI,CAAC,KAAK,GAAG,IAAI,QAAQ,IAAI,CAAC,QAAQ,EAAE;YACtC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAA;YACd,MAAK;SACN;QAED,QAAQ,GAAG,IAAI,CAAA;QACf,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,IAAI,CAAC,QAAQ,EAAE;gBACb,QAAQ,GAAG,IAAI,CAAA;gBACf,CAAC,EAAE,CAAA;gBACH,SAAQ;aACT;YACD,0DAA0D;SAC3D;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE;YAC1B,4DAA4D;YAC5D,KAAK,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;gBAChE,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE;oBAC3B,+CAA+C;oBAC/C,IAAI,UAAU,EAAE;wBACd,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE,IAAI,CAAC,CAAA;qBAC9C;oBACD,CAAC,IAAI,GAAG,CAAC,MAAM,CAAA;oBACf,IAAI,GAAG;wBAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;;wBACnB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oBACtB,KAAK,GAAG,KAAK,IAAI,CAAC,CAAA;oBAClB,SAAS,KAAK,CAAA;iBACf;aACF;SACF;QAED,gDAAgD;QAChD,QAAQ,GAAG,KAAK,CAAA;QAChB,IAAI,UAAU,EAAE;YACd,sDAAsD;YACtD,mBAAmB;YACnB,IAAI,CAAC,GAAG,UAAU,EAAE;gBAClB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;aAC5D;iBAAM,IAAI,CAAC,KAAK,UAAU,EAAE;gBAC3B,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;aAC5B;YACD,UAAU,GAAG,EAAE,CAAA;YACf,CAAC,EAAE,CAAA;YACH,SAAQ;SACT;QAED,qCAAqC;QACrC,8DAA8D;QAC9D,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE;YAChC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;YACjC,CAAC,IAAI,CAAC,CAAA;YACN,SAAQ;SACT;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE;YAC/B,UAAU,GAAG,CAAC,CAAA;YACd,CAAC,IAAI,CAAC,CAAA;YACN,SAAQ;SACT;QAED,oDAAoD;QACpD,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;QAC3B,CAAC,EAAE,CAAA;KACJ;IAED,IAAI,MAAM,GAAG,CAAC,EAAE;QACd,sDAAsD;QACtD,+CAA+C;QAC/C,OAAO,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAA;KAC7B;IAED,gEAAgE;IAChE,kEAAkE;IAClE,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAClC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE,IAAI,CAAC,CAAA;KAC9C;IAED,yEAAyE;IACzE,sEAAsE;IACtE,yEAAyE;IACzE,sEAAsE;IACtE,IACE,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,MAAM,CAAC,MAAM,KAAK,CAAC;QACnB,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,MAAM,EACP;QACA,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAClE,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG,EAAE,KAAK,CAAC,CAAA;KACrD;IAED,MAAM,OAAO,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;IACxE,MAAM,KAAK,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,CAAA;IACpE,MAAM,IAAI,GACR,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;QAC1B,CAAC,CAAC,GAAG,GAAG,OAAO,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG;QACnC,CAAC,CAAC,MAAM,CAAC,MAAM;YACf,CAAC,CAAC,OAAO;YACT,CAAC,CAAC,KAAK,CAAA;IAEX,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG,EAAE,IAAI,CAAC,CAAA;AAC1C,CAAC,CAAA", "sourcesContent": ["// translate the various posix character classes into unicode properties\n// this works across all unicode locales\n\n// { <posix class>: [<translation>, /u flag required, negated]\nconst posixClasses: { [k: string]: [e: string, u: boolean, n?: boolean] } = {\n  '[:alnum:]': ['\\\\p{L}\\\\p{Nl}\\\\p{Nd}', true],\n  '[:alpha:]': ['\\\\p{L}\\\\p{Nl}', true],\n  '[:ascii:]': ['\\\\x' + '00-\\\\x' + '7f', false],\n  '[:blank:]': ['\\\\p{Zs}\\\\t', true],\n  '[:cntrl:]': ['\\\\p{Cc}', true],\n  '[:digit:]': ['\\\\p{Nd}', true],\n  '[:graph:]': ['\\\\p{Z}\\\\p{C}', true, true],\n  '[:lower:]': ['\\\\p{Ll}', true],\n  '[:print:]': ['\\\\p{C}', true],\n  '[:punct:]': ['\\\\p{P}', true],\n  '[:space:]': ['\\\\p{Z}\\\\t\\\\r\\\\n\\\\v\\\\f', true],\n  '[:upper:]': ['\\\\p{Lu}', true],\n  '[:word:]': ['\\\\p{L}\\\\p{Nl}\\\\p{Nd}\\\\p{Pc}', true],\n  '[:xdigit:]': ['A-Fa-f0-9', false],\n}\n\n// only need to escape a few things inside of brace expressions\n// escapes: [ \\ ] -\nconst braceEscape = (s: string) => s.replace(/[[\\]\\\\-]/g, '\\\\$&')\n// escape all regexp magic characters\nconst regexpEscape = (s: string) =>\n  s.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&')\n\n// everything has already been escaped, we just have to join\nconst rangesToString = (ranges: string[]): string => ranges.join('')\n\nexport type ParseClassResult = [\n  src: string,\n  uFlag: boolean,\n  consumed: number,\n  hasMagic: boolean\n]\n\n// takes a glob string at a posix brace expression, and returns\n// an equivalent regular expression source, and boolean indicating\n// whether the /u flag needs to be applied, and the number of chars\n// consumed to parse the character class.\n// This also removes out of order ranges, and returns ($.) if the\n// entire class just no good.\nexport const parseClass = (\n  glob: string,\n  position: number\n): ParseClassResult => {\n  const pos = position\n  /* c8 ignore start */\n  if (glob.charAt(pos) !== '[') {\n    throw new Error('not in a brace expression')\n  }\n  /* c8 ignore stop */\n  const ranges: string[] = []\n  const negs: string[] = []\n\n  let i = pos + 1\n  let sawStart = false\n  let uflag = false\n  let escaping = false\n  let negate = false\n  let endPos = pos\n  let rangeStart = ''\n  WHILE: while (i < glob.length) {\n    const c = glob.charAt(i)\n    if ((c === '!' || c === '^') && i === pos + 1) {\n      negate = true\n      i++\n      continue\n    }\n\n    if (c === ']' && sawStart && !escaping) {\n      endPos = i + 1\n      break\n    }\n\n    sawStart = true\n    if (c === '\\\\') {\n      if (!escaping) {\n        escaping = true\n        i++\n        continue\n      }\n      // escaped \\ char, fall through and treat like normal char\n    }\n    if (c === '[' && !escaping) {\n      // either a posix class, a collation equivalent, or just a [\n      for (const [cls, [unip, u, neg]] of Object.entries(posixClasses)) {\n        if (glob.startsWith(cls, i)) {\n          // invalid, [a-[] is fine, but not [a-[:alpha]]\n          if (rangeStart) {\n            return ['$.', false, glob.length - pos, true]\n          }\n          i += cls.length\n          if (neg) negs.push(unip)\n          else ranges.push(unip)\n          uflag = uflag || u\n          continue WHILE\n        }\n      }\n    }\n\n    // now it's just a normal character, effectively\n    escaping = false\n    if (rangeStart) {\n      // throw this range away if it's not valid, but others\n      // can still match.\n      if (c > rangeStart) {\n        ranges.push(braceEscape(rangeStart) + '-' + braceEscape(c))\n      } else if (c === rangeStart) {\n        ranges.push(braceEscape(c))\n      }\n      rangeStart = ''\n      i++\n      continue\n    }\n\n    // now might be the start of a range.\n    // can be either c-d or c-] or c<more...>] or c] at this point\n    if (glob.startsWith('-]', i + 1)) {\n      ranges.push(braceEscape(c + '-'))\n      i += 2\n      continue\n    }\n    if (glob.startsWith('-', i + 1)) {\n      rangeStart = c\n      i += 2\n      continue\n    }\n\n    // not the start of a range, just a single character\n    ranges.push(braceEscape(c))\n    i++\n  }\n\n  if (endPos < i) {\n    // didn't see the end of the class, not a valid class,\n    // but might still be valid as a literal match.\n    return ['', false, 0, false]\n  }\n\n  // if we got no ranges and no negates, then we have a range that\n  // cannot possibly match anything, and that poisons the whole glob\n  if (!ranges.length && !negs.length) {\n    return ['$.', false, glob.length - pos, true]\n  }\n\n  // if we got one positive range, and it's a single character, then that's\n  // not actually a magic pattern, it's just that one literal character.\n  // we should not treat that as \"magic\", we should just return the literal\n  // character. [_] is a perfectly valid way to escape glob magic chars.\n  if (\n    negs.length === 0 &&\n    ranges.length === 1 &&\n    /^\\\\?.$/.test(ranges[0]) &&\n    !negate\n  ) {\n    const r = ranges[0].length === 2 ? ranges[0].slice(-1) : ranges[0]\n    return [regexpEscape(r), false, endPos - pos, false]\n  }\n\n  const sranges = '[' + (negate ? '^' : '') + rangesToString(ranges) + ']'\n  const snegs = '[' + (negate ? '' : '^') + rangesToString(negs) + ']'\n  const comb =\n    ranges.length && negs.length\n      ? '(' + sranges + '|' + snegs + ')'\n      : ranges.length\n      ? sranges\n      : snegs\n\n  return [comb, uflag, endPos - pos, true]\n}\n"]}